/// شاشة إدارة الضرائب السورية المتقدمة
/// تدير جميع أنواع الضرائب السورية مع التقارير المتخصصة
library;

import 'package:flutter/material.dart';
import '../models/tax_calculation.dart';
import '../services/syrian_tax_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';

class SyrianTaxManagementScreen extends StatefulWidget {
  const SyrianTaxManagementScreen({super.key});

  @override
  State<SyrianTaxManagementScreen> createState() => _SyrianTaxManagementScreenState();
}

class _SyrianTaxManagementScreenState extends State<SyrianTaxManagementScreen>
    with TickerProviderStateMixin {
  final SyrianTaxService _taxService = SyrianTaxService();
  
  late TabController _tabController;
  bool _isLoading = true;
  
  // بيانات الضرائب
  Map<String, dynamic> _taxSummary = {};
  List<TaxCalculation> _recentCalculations = [];
  Map<String, bool> _taxSettings = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadTaxData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadTaxData() async {
    setState(() => _isLoading = true);
    try {
      final summary = await _taxService.getTaxSummary();
      final calculations = await _taxService.getRecentCalculations();
      final settings = await _taxService.getTaxSettings();
      
      setState(() {
        _taxSummary = summary;
        _recentCalculations = calculations;
        _taxSettings = settings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل بيانات الضرائب: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h2('إدارة الضرائب السورية'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _generateTaxReport,
            icon: const Icon(Icons.assessment),
            tooltip: 'تقرير ضريبي',
          ),
          IconButton(
            onPressed: _exportTaxData,
            icon: const Icon(Icons.file_download),
            tooltip: 'تصدير البيانات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'لوحة التحكم'),
            Tab(icon: Icon(Icons.calculate), text: 'حاسبة الضرائب'),
            Tab(icon: Icon(Icons.receipt), text: 'التقارير'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDashboardTab(),
                _buildCalculatorTab(),
                _buildReportsTab(),
                _buildSettingsTab(),
              ],
            ),
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص الضرائب
          const Text(
            'ملخص الضرائب',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildTaxSummaryCard(
                  'ضريبة الدخل',
                  _taxSummary['income_tax']?.toString() ?? '0',
                  'ل.س',
                  Icons.account_balance_wallet,
                  RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTaxSummaryCard(
                  'ضريبة القيمة المضافة',
                  _taxSummary['vat']?.toString() ?? '0',
                  'ل.س',
                  Icons.receipt,
                  RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildTaxSummaryCard(
                  'الضرائب المستحقة',
                  _taxSummary['due_taxes']?.toString() ?? '0',
                  'ل.س',
                  Icons.warning,
                  RevolutionaryColors.warningGlow,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTaxSummaryCard(
                  'الضرائب المدفوعة',
                  _taxSummary['paid_taxes']?.toString() ?? '0',
                  'ل.س',
                  Icons.check_circle,
                  RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // الحسابات الأخيرة
          const Text(
            'الحسابات الضريبية الأخيرة',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          if (_recentCalculations.isEmpty)
            const Center(
              child: Text(
                'لا توجد حسابات ضريبية',
                style: TextStyle(color: Colors.grey),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _recentCalculations.length,
              itemBuilder: (context, index) {
                final calculation = _recentCalculations[index];
                return _buildCalculationCard(calculation);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildCalculatorTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // حاسبة ضريبة الدخل
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.calculate,
                        color: RevolutionaryColors.damascusSky,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'حاسبة ضريبة الدخل',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'الدخل السنوي (ل.س)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.money),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'فئة الضريبة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'individual', child: Text('فرد')),
                      DropdownMenuItem(value: 'company', child: Text('شركة')),
                      DropdownMenuItem(value: 'freelancer', child: Text('مهنة حرة')),
                    ],
                    onChanged: (value) {},
                  ),
                  const SizedBox(height: 16),
                  
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _calculateIncomeTax,
                      icon: const Icon(Icons.calculate),
                      label: const Text('حساب الضريبة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.damascusSky,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // حاسبة ضريبة القيمة المضافة
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.receipt,
                        color: RevolutionaryColors.successGlow,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'حاسبة ضريبة القيمة المضافة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'المبلغ قبل الضريبة (ل.س)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.money),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 16),
                  
                  DropdownButtonFormField<double>(
                    decoration: const InputDecoration(
                      labelText: 'نسبة الضريبة',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 0.0, child: Text('معفى من الضريبة')),
                      DropdownMenuItem(value: 5.0, child: Text('5%')),
                      DropdownMenuItem(value: 10.0, child: Text('10%')),
                      DropdownMenuItem(value: 15.0, child: Text('15%')),
                    ],
                    onChanged: (value) {},
                  ),
                  const SizedBox(height: 16),
                  
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _calculateVAT,
                      icon: const Icon(Icons.calculate),
                      label: const Text('حساب ضريبة القيمة المضافة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: RevolutionaryColors.successGlow,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'التقارير الضريبية',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // تقارير ضريبة الدخل
          _buildReportCard(
            'تقرير ضريبة الدخل السنوي',
            'تقرير شامل لضريبة الدخل للسنة المالية',
            Icons.account_balance_wallet,
            RevolutionaryColors.damascusSky,
            () => _generateIncomeReport(),
          ),
          
          // تقارير ضريبة القيمة المضافة
          _buildReportCard(
            'تقرير ضريبة القيمة المضافة',
            'تقرير شهري/ربع سنوي لضريبة القيمة المضافة',
            Icons.receipt,
            RevolutionaryColors.successGlow,
            () => _generateVATReport(),
          ),
          
          // تقرير الضرائب المستحقة
          _buildReportCard(
            'تقرير الضرائب المستحقة',
            'قائمة بجميع الضرائب المستحقة والمواعيد',
            Icons.warning,
            RevolutionaryColors.warningGlow,
            () => _generateDueTaxesReport(),
          ),
          
          // تقرير الضرائب المدفوعة
          _buildReportCard(
            'تقرير الضرائب المدفوعة',
            'سجل بجميع الضرائب المدفوعة مع الإيصالات',
            Icons.check_circle,
            RevolutionaryColors.successGlow,
            () => _generatePaidTaxesReport(),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الضرائب',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          
          // تفعيل/إلغاء أنواع الضرائب
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('ضريبة الدخل'),
                  subtitle: const Text('تفعيل حساب ضريبة الدخل'),
                  value: _taxSettings['income_tax'] ?? true,
                  onChanged: (value) => _updateTaxSetting('income_tax', value),
                  activeColor: RevolutionaryColors.damascusSky,
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('ضريبة القيمة المضافة'),
                  subtitle: const Text('تفعيل حساب ضريبة القيمة المضافة'),
                  value: _taxSettings['vat'] ?? true,
                  onChanged: (value) => _updateTaxSetting('vat', value),
                  activeColor: RevolutionaryColors.damascusSky,
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('ضريبة الأرباح التجارية'),
                  subtitle: const Text('تفعيل حساب ضريبة الأرباح التجارية'),
                  value: _taxSettings['business_profit'] ?? false,
                  onChanged: (value) => _updateTaxSetting('business_profit', value),
                  activeColor: RevolutionaryColors.damascusSky,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // إعدادات النسب الضريبية
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'النسب الضريبية',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'نسبة ضريبة القيمة المضافة الافتراضية (%)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: '10',
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'الحد الأدنى المعفى من ضريبة الدخل (ل.س)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: '500000',
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // إعدادات التقارير
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('تذكيرات الضرائب'),
                  subtitle: const Text('تفعيل تذكيرات مواعيد الضرائب'),
                  trailing: Switch(
                    value: true,
                    onChanged: (value) {},
                    activeColor: RevolutionaryColors.damascusSky,
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.email),
                  title: const Text('إرسال التقارير بالبريد'),
                  subtitle: const Text('إرسال التقارير الضريبية تلقائياً'),
                  trailing: Switch(
                    value: false,
                    onChanged: (value) {},
                    activeColor: RevolutionaryColors.damascusSky,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxSummaryCard(
    String title,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            unit,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationCard(TaxCalculation calculation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: RevolutionaryColors.damascusSky.withOpacity(0.1),
          child: Icon(
            Icons.calculate,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        title: Text(calculation.type),
        subtitle: Text('${calculation.amount} ل.س'),
        trailing: Text(
          _formatDate(calculation.date),
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ),
    );
  }

  Widget _buildReportCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _calculateIncomeTax() {
    // TODO: تنفيذ حساب ضريبة الدخل
    _showInfoSnackBar('حاسبة ضريبة الدخل قيد التطوير');
  }

  void _calculateVAT() {
    // TODO: تنفيذ حساب ضريبة القيمة المضافة
    _showInfoSnackBar('حاسبة ضريبة القيمة المضافة قيد التطوير');
  }

  void _generateTaxReport() {
    // TODO: تنفيذ إنشاء التقرير الضريبي
    _showInfoSnackBar('إنشاء التقرير الضريبي قيد التطوير');
  }

  void _exportTaxData() {
    // TODO: تنفيذ تصدير البيانات الضريبية
    _showInfoSnackBar('تصدير البيانات الضريبية قيد التطوير');
  }

  void _generateIncomeReport() {
    // TODO: تنفيذ تقرير ضريبة الدخل
    _showInfoSnackBar('تقرير ضريبة الدخل قيد التطوير');
  }

  void _generateVATReport() {
    // TODO: تنفيذ تقرير ضريبة القيمة المضافة
    _showInfoSnackBar('تقرير ضريبة القيمة المضافة قيد التطوير');
  }

  void _generateDueTaxesReport() {
    // TODO: تنفيذ تقرير الضرائب المستحقة
    _showInfoSnackBar('تقرير الضرائب المستحقة قيد التطوير');
  }

  void _generatePaidTaxesReport() {
    // TODO: تنفيذ تقرير الضرائب المدفوعة
    _showInfoSnackBar('تقرير الضرائب المدفوعة قيد التطوير');
  }

  void _updateTaxSetting(String key, bool value) {
    setState(() {
      _taxSettings[key] = value;
    });
    // TODO: حفظ الإعدادات في قاعدة البيانات
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }
}
