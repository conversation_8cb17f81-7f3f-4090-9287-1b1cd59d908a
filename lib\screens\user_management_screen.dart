/// شاشة إدارة المستخدمين والصلاحيات
/// تدير المستخدمين وأدوارهم وصلاحياتهم في النظام
library;

import 'package:flutter/material.dart';
import '../models/user.dart';
import '../models/user_role.dart';
import '../services/user_service.dart';
import '../services/auth_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen>
    with TickerProviderStateMixin {
  final UserService _userService = UserService();
  final AuthService _authService = AuthService();

  late TabController _tabController;
  List<User> _users = [];
  List<UserRole> _roles = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final users = await _userService.getAllUsers();
      final roles = await _userService.getAllRoles();
      
      setState(() {
        _users = users;
        _roles = roles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h2('إدارة المستخدمين'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.people), text: 'المستخدمين'),
            Tab(icon: Icon(Icons.admin_panel_settings), text: 'الأدوار'),
            Tab(icon: Icon(Icons.security), text: 'الصلاحيات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildUsersTab(),
                _buildRolesTab(),
                _buildPermissionsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddUserDialog,
        backgroundColor: RevolutionaryColors.damascusSky,
        icon: const Icon(Icons.person_add, color: Colors.white),
        label: const Text('إضافة مستخدم', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  Widget _buildUsersTab() {
    final filteredUsers = _users.where((user) =>
        user.username.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        user.fullName.toLowerCase().contains(_searchQuery.toLowerCase())).toList();

    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            decoration: InputDecoration(
              hintText: 'البحث في المستخدمين...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
        ),
        
        // قائمة المستخدمين
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: filteredUsers.length,
            itemBuilder: (context, index) {
              final user = filteredUsers[index];
              return _buildUserCard(user);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUserCard(User user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: user.isActive 
              ? RevolutionaryColors.damascusSky 
              : Colors.grey,
          child: Text(
            user.fullName.isNotEmpty ? user.fullName[0].toUpperCase() : 'U',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          user.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم المستخدم: ${user.username}'),
            Text('الدور: ${user.roleName ?? 'غير محدد'}'),
            Text('آخر دخول: ${_formatDateTime(user.lastLoginAt)}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر الحالة
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: user.isActive ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              onSelected: (value) => _handleUserAction(value, user),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'permissions',
                  child: ListTile(
                    leading: Icon(Icons.security),
                    title: Text('الصلاحيات'),
                  ),
                ),
                PopupMenuItem(
                  value: user.isActive ? 'deactivate' : 'activate',
                  child: ListTile(
                    leading: Icon(user.isActive ? Icons.block : Icons.check_circle),
                    title: Text(user.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('حذف', style: TextStyle(color: Colors.red)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRolesTab() {
    return Column(
      children: [
        // إحصائيات الأدوار
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الأدوار',
                  _roles.length.toString(),
                  Icons.admin_panel_settings,
                  RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'الأدوار النشطة',
                  _roles.where((r) => r.isActive).length.toString(),
                  Icons.check_circle,
                  RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ),
        ),
        
        // قائمة الأدوار
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _roles.length,
            itemBuilder: (context, index) {
              final role = _roles[index];
              return _buildRoleCard(role);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRoleCard(UserRole role) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: Icon(
          Icons.admin_panel_settings,
          color: role.isActive ? RevolutionaryColors.damascusSky : Colors.grey,
        ),
        title: Text(
          role.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(role.description),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الصلاحيات:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: RevolutionaryColors.damascusSky,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: role.permissions.map((permission) {
                    return Chip(
                      label: Text(
                        permission,
                        style: const TextStyle(fontSize: 12),
                      ),
                      backgroundColor: RevolutionaryColors.damascusSky.withOpacity(0.1),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () => _editRole(role),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed: () => _deleteRole(role),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      label: const Text('حذف', style: TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsTab() {
    return const Center(
      child: Text(
        'إدارة الصلاحيات\nقريباً...',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  void _handleUserAction(String action, User user) {
    switch (action) {
      case 'edit':
        _editUser(user);
        break;
      case 'permissions':
        _manageUserPermissions(user);
        break;
      case 'activate':
      case 'deactivate':
        _toggleUserStatus(user);
        break;
      case 'delete':
        _deleteUser(user);
        break;
    }
  }

  void _showAddUserDialog() {
    // TODO: تنفيذ حوار إضافة مستخدم جديد
    _showInfoSnackBar('ميزة إضافة المستخدمين قيد التطوير');
  }

  void _editUser(User user) {
    // TODO: تنفيذ تعديل المستخدم
    _showInfoSnackBar('ميزة تعديل المستخدم قيد التطوير');
  }

  void _manageUserPermissions(User user) {
    // TODO: تنفيذ إدارة صلاحيات المستخدم
    _showInfoSnackBar('ميزة إدارة الصلاحيات قيد التطوير');
  }

  void _toggleUserStatus(User user) {
    // TODO: تنفيذ تفعيل/إلغاء تفعيل المستخدم
    _showInfoSnackBar('ميزة تغيير حالة المستخدم قيد التطوير');
  }

  void _deleteUser(User user) {
    // TODO: تنفيذ حذف المستخدم
    _showInfoSnackBar('ميزة حذف المستخدم قيد التطوير');
  }

  void _editRole(UserRole role) {
    // TODO: تنفيذ تعديل الدور
    _showInfoSnackBar('ميزة تعديل الأدوار قيد التطوير');
  }

  void _deleteRole(UserRole role) {
    // TODO: تنفيذ حذف الدور
    _showInfoSnackBar('ميزة حذف الأدوار قيد التطوير');
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'لم يسجل دخول';
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }
}
